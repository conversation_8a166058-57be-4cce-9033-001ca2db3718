<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <!-- <el-form-item label="异常处理单" prop="abnormalNo">
          <el-input v-model="queryParams.abnormalNo" placeholder="请输入异常处理单" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item> -->
        <!-- <el-form-item label="质检单id" prop="inspBillId">
        <el-input
          v-model="queryParams.inspBillId"
          placeholder="请输入质检单id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <el-form-item label="质检单号" prop="inspBillNo">
          <el-input
            v-model="queryParams.inspBillNo"
            placeholder="请输入质检单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <!-- <el-form-item label="异常发生日期" prop="abnormalDate">
          <el-date-picker clearable v-model="queryParams.abnormalDate" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择异常发生日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="总数量" prop="qty">
          <el-input v-model="queryParams.qty" placeholder="请输入总数量" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="不良数量" prop="poorCount">
          <el-input v-model="queryParams.poorCount" placeholder="请输入不良数量" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="异常来源" prop="abnormalSource">
          <el-input v-model="queryParams.abnormalSource" placeholder="请输入异常来源" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="发生频率" prop="occursFrequency">
          <el-input v-model="queryParams.occursFrequency" placeholder="请输入发生频率" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="不良率" prop="poorRate">
          <el-input v-model="queryParams.poorRate" placeholder="请输入不良率" clearable @keyup.enter.native="handleQuery" />
        </el-form-item> -->
        <!-- <el-form-item label="物料id" prop="materialId">
        <el-input
          v-model="queryParams.materialId"
          placeholder="请输入物料id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <!-- <el-form-item label="物料编码" prop="materialCode">
          <el-input v-model="queryParams.materialCode" placeholder="请输入物料编码" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item> -->
        <el-form-item label="物料名称" prop="materialName">
          <el-input
            v-model="queryParams.materialName"
            placeholder="请输入物料名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <!-- <el-form-item label="临时解决措施" prop="interimMeasures">
          <el-input v-model="queryParams.interimMeasures" placeholder="请输入临时解决措施" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="原因分析" prop="reason">
          <el-input v-model="queryParams.reason" placeholder="请输入原因分析" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="预防措施" prop="preventiveMeasure">
          <el-input v-model="queryParams.preventiveMeasure" placeholder="请输入预防措施" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item> -->
        <el-form-item label="处理结果" prop="results">
          <!-- <el-input v-model="queryParams.results" placeholder="请输入处理结果" clearable @keyup.enter.native="handleQuery" /> -->
          <el-select
            v-model="queryParams.results"
            placeholder="请选择"
            clearable
            @keyup.enter.native="handleQuery"
          >
            <el-option
              v-for="dict in dict.type.results"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="图片" prop="picture">
          <el-input v-model="queryParams.picture" placeholder="请输入图片" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="组织" prop="comId">
          <el-input v-model="queryParams.comId" placeholder="请输入组织" clearable @keyup.enter.native="handleQuery" />
        </el-form-item> -->
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <!-- <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
            v-hasPermi="['quality:abnormal:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
            v-hasPermi="['quality:abnormal:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['quality:abnormal:remove']">删除</el-button>
        </el-col> -->
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['quality:abnormal:export']"
            >导出</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        height="62vh"
        v-loading="loading"
        :data="abnormalList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="index" width="55" align="center" />
        <el-table-column type="selection" width="55" align="center" />

        <el-table-column
          label="异常处理单"
          align="center"
          prop="abnormalNo"
          :width="180"
        />

        <el-table-column label="质检单号" align="center" prop="inspBillNo"  :width="tableWidth(abnormalList.map(x => x.inspBillNo))"/>
        <el-table-column
          label="异常发生日期"
          align="center"
          prop="abnormalDate"
          :width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.abnormalDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="总数量" align="center" prop="qty" />
        <el-table-column label="不良数量" align="center" prop="poorCount" />
        <el-table-column label="异常来源" align="center" prop="abnormalSource">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.abnormal_source"
              :value="scope.row.abnormalSource"
            />
          </template>
        </el-table-column>
        <el-table-column label="发生频率" align="center" prop="occursFrequency">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.occurs_frequency"
              :value="scope.row.occursFrequency"
            />
          </template>
        </el-table-column>
        <el-table-column label="不良率" align="center" prop="poorRate" />
        <el-table-column label="物料编码" align="center" prop="materialCode" />
        <el-table-column label="物料名称" align="center" prop="materialName" />
        <el-table-column
          label="异常原因描述"
          align="center"
          prop="abnormalDesc"
          width="180"
        />
        <el-table-column
          label="临时解决措施"
          align="center"
          prop="interimMeasures"
          width="180"
        />
        <el-table-column label="原因分析" align="center" prop="reason" />
        <el-table-column
          label="预防措施"
          align="center"
          prop="preventiveMeasure"
        />
        <el-table-column label="处理结果" align="center" prop="results">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.results" :value="scope.row.results" />
          </template>
        </el-table-column>
        <!-- <el-table-column label="图片" align="center" prop="picture" /> -->
        <el-table-column label="图片" align="center">
          <template slot-scope="scope">
            <div
              v-if="scope.row.picture"
              style="display: flex; justify-content: center"
            >
              <el-image
                style="width: 50px; height: 50px; cursor: pointer"
                :src="getImageUrl(scope.row.picture)"
                :preview-src-list="[getImageUrl(scope.row.picture)]"
                fit="cover"
                :z-index="3000"
              >
                <div slot="error" class="image-slot">
                  <i
                    class="el-icon-picture-outline"
                    style="font-size: 20px"
                  ></i>
                </div>
              </el-image>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="组织" align="center" prop="comId" />
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="160"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['quality:abnormal:view']"
              >查看</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['quality:abnormal:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['quality:abnormal:remove']"
              >删除</el-button
            >
            <!-- 挑选按钮 - 只有当处理结果为挑选时才显示 -->
            <el-button
              v-if="isPickingResult(scope.row.results)"
              size="mini"
              type="text"
              icon="el-icon-s-operation"
              @click="handlePicking(scope.row)"
              style="color: #409EFF;"
              >挑选</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改单据异常反馈单对话框 -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules">
          <el-row :gutter="10" class="mb8">
            <el-col :span="12">
              <el-form-item
                label="异常处理单"
                prop="abnormalNo"
                style="width: 240px"
              >
                <el-input
                  v-model="form.abnormalNo"
                  placeholder="请输入异常处理单"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="异常发生日期"
                prop="abnormalDate"
                style="width: 240px"
              >
                <el-date-picker
                  clearable
                  v-model="form.abnormalDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择异常发生日期"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 异常信息详情 -->
          <el-collapse v-model="activeNames">
            <el-collapse-item title="异常信息详情" name="1">
              <el-row :gutter="10" class="mb8">
                <el-col :span="12">
                  <el-form-item label="总数量" prop="qty" style="width: 240px">
                    <el-input
                      v-model="form.qty"
                      placeholder="请输入总数量"
                      disabled
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="不良数量"
                    prop="poorCount"
                    style="width: 240px"
                  >
                    <el-input
                      v-model="form.poorCount"
                      placeholder="请输入不良数量"
                      disabled
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10" class="mb8">
                <el-col :span="12">
                  <el-form-item
                    label="异常来源"
                    prop="abnormalSource"
                    style="width: 240px"
                  >
                    <el-select v-model="form.abnormalSource" :disabled="isView">
                      <el-option
                        v-for="dict in dict.type.abnormal_source"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10" class="mb8">
                <el-col :span="12">
                  <el-form-item
                    label="发生频率"
                    prop="occursFrequency"
                    style="width: 240px"
                  >
                    <!-- <el-input v-model="form.occursFrequency" placeholder="请输入发生频率" /> -->
                    <el-select
                      v-model="form.occursFrequency"
                      :disabled="isView"
                    >
                      <el-option
                        v-for="dict in dict.type.occurs_frequency"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="不良率(%)"
                    prop="poorRate"
                    style="width: 240px"
                  >
                    <el-input
                      v-model="form.poorRate"
                      placeholder="请输入不良率"
                      disabled
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10" class="mb8">
                <el-col :span="12">
                  <el-form-item
                    label="质检单号"
                    prop="inspBillNo"
                    style="width: 240px"
                  >
                    <el-input
                      v-model="form.inspBillNo"
                      placeholder="请输入质检单号"
                      disabled
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="物料名称"
                    prop="materialName"
                    style="width: 240px"
                  >
                    <el-input
                      v-model="form.materialName"
                      placeholder="请输入物料名称"
                      disabled
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10" class="mb8">
                <el-col>
                  <el-form-item
                    label="异常原因描述"
                    prop="abnormalDesc"
                    style="width: 700px"
                  >
                    <el-input
                      v-model="form.abnormalDesc"
                      type="textarea"
                      placeholder="请输入内容"
                      :rows="5"
                      maxlength="200"
                      :disabled="isView"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10" class="mb8">
                <el-col>
                  <el-form-item
                    label="临时解决措施"
                    prop="interimMeasures"
                    style="width: 700px"
                  >
                    <el-input
                      v-model="form.interimMeasures"
                      type="textarea"
                      placeholder="请输入临时解决措施"
                      :rows="5"
                      maxlength="200"
                      :disabled="isView"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10" class="mb8">
                <el-col>
                  <el-form-item
                    label="原因分析"
                    prop="reason"
                    style="width: 700px"
                  >
                    <el-input
                      v-model="form.reason"
                      type="textarea"
                      placeholder="请输入原因分析"
                      :rows="5"
                      maxlength="200"
                      show-word-limit
                      :disabled="isView"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10" class="mb8">
                <el-col>
                  <el-form-item
                    label="预防措施"
                    prop="preventiveMeasure"
                    style="width: 700px"
                  >
                    <el-input
                      v-model="form.preventiveMeasure"
                      type="textarea"
                      placeholder="请输入预防措施"
                      :rows="5"
                      maxlength="200"
                      show-word-limit
                      :disabled="isView"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10" class="mb8">
                <el-col :span="12">
                  <el-form-item
                    label="处理结果"
                    prop="results"
                    style="width: 240px"
                  >
                    <!-- <el-input v-model="form.results" placeholder="请输入处理结果" /> -->
                    <el-select v-model="form.results" :disabled="isView">
                      <el-option
                        v-for="dict in dict.type.results"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <!-- <el-form-item label="图片" prop="picture" style="width: 240px;">
                    <el-input v-model="form.picture" placeholder="请输入图片" />
                  </el-form-item> -->
                  <el-form-item label="图片" prop="picture" label-width="180px">
                    <image-upload v-model="form.picture" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-collapse-item>
          </el-collapse>
        </el-form>
        <div class="demo-drawer__footer">
          <template v-if="!isView">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </template>
          <template v-else>
            <el-button @click="cancel">关 闭</el-button>
          </template>
        </div>
      </el-drawer>

      <!-- 挑选对话框 -->
      <el-dialog
        :title="`编辑【异常处理单:${pickingForm.abnormalNo}】`"
        :visible.sync="pickingDialogVisible"
        width="80%"
        append-to-body
        :close-on-click-modal="false"
        top="5vh"
      >
        <div style="margin-bottom: 20px;">
          
              <div style="display: flex; align-items: center; margin-bottom: 10px;">
                <span style="width: 80px; text-align: right; margin-right: 10px;">标签号</span>
                <el-input
                  v-model="pickingForm.labelNo"
                  placeholder="请输入"
                  style="flex: 1;"
                />
              </div>
              <div style="display: flex; align-items: center; margin-bottom: 10px;">
                <span style="width: 80px; text-align: right; margin-right: 10px;">总数量</span>
                <el-input
                  v-model="pickingForm.totalQty"
                  disabled
                  style="flex: 1;"
                />
              </div>
        </div>

        <!-- 挑选明细表格 -->
        <el-table
          :data="pickingDetailList"
          border
          style="width: 100%; margin-bottom: 20px;"
          height="300px"
        >
          <el-table-column type="index"  width="60" align="center" />
          <el-table-column prop="labelNo" label="标签号" min-width="120" align="center" />
          <el-table-column prop="boxNo" label="箱号" min-width="100" align="center" />
          <el-table-column prop="materialCode" label="物料编码" min-width="150" align="center" />
          <el-table-column prop="materialName" label="物料名称" min-width="180" align="center" />
          <el-table-column prop="quantity" label="数量" min-width="100" align="center" />
          <el-table-column prop="qualifiedQty" label="合格数" min-width="120" align="center">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.qualifiedQty"
                :min="0"
                :max="scope.row.quantity"
                size="mini"
                style="width: 100%;"
                @change="handleQualifiedQtyChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="unqualifiedQty" label="不合格数" min-width="120" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.quantity - scope.row.qualifiedQty }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDeletePickingDetail(scope.$index)"
                style="color: #f56c6c;"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 空数据提示 -->
        <div v-if="pickingDetailList.length === 0" style="text-align: center; color: #999; padding: 40px;">
          暂无数据
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button @click="cancelPicking">取消</el-button>
          <el-button type="primary" @click="submitPicking">确认</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  listAbnormal,
  getAbnormal,
  delAbnormal,
  addAbnormal,
  updateAbnormal,
  queryReceiveBox,
  confirmPicking,
} from "@/api/quality/abnormal";
import { listBill } from "@/api/quality/bill";
import { listResult } from "@/api/quality/result";

export default {
  name: "Abnormal",
  dicts: ["abnormal_source", "occurs_frequency", "results"],
  data() {
    return {
      // 遮罩层
      loading: true,
      activeNames: ["1"],
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 单据异常反馈单表格数据
      abnormalList: [],
      // 弹出层标题
      title: "",
      isView: false, // 是否是查看模式
      // 是否显示弹出层
      open: false,
      // 挑选对话框相关
      pickingDialogVisible: false,
      pickingForm: {
        abnormalNo: '',
        inspBillNo: '',
        materialName: '',
        materialCode: '',
        totalQty: 0,
        labelNo: ''
      },
      // 挑选明细列表
      pickingDetailList: [
        
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        abnormalNo: null,
        inspBillId: null,
        inspBillNo: null,
        abnormalDate: null,
        qty: null,
        poorCount: null,
        abnormalSource: null,
        occursFrequency: null,
        poorRate: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        abnormalDesc: null,
        interimMeasures: null,
        reason: null,
        preventiveMeasure: null,
        results: null,
        picture: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        qty: [{ required: true, message: "总不能为空", trigger: "blur" }],
        abnormalSource: [
          { required: true, message: "异常来源不能为空", trigger: "blur" },
        ],
        inspBillNo: [
          { required: true, message: "质检单号不能为空", trigger: "blur" },
        ],
        materialName: [
          { required: true, message: "物料名称不能为空", trigger: "blur" },
        ],
        results: [
          { required: true, message: "处理结果不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    // 处理从评审反馈页面跳转过来的情况
    this.handleFeedbackRoute();
  },
  methods: {
    // 处理从评审反馈页面跳转过来的路由
    async handleFeedbackRoute() {
      const { iqcNo, mode } = this.$route.query;
      if (mode === "feedback" && iqcNo) {
        // 检查是否已存在该质检单号的异常反馈单
        await this.checkExistingAbnormal(iqcNo);
      }
    },

    // 检查是否已存在异常反馈单
    async checkExistingAbnormal(iqcNo) {
      try {
        // 根据质检单号查询异常反馈单
        const queryParams = {
          pageNum: 1,
          pageSize: 1,
          inspBillNo: iqcNo,
        };

        const response = await listAbnormal(queryParams);

        if (response.rows && response.rows.length > 0) {
          // 存在异常反馈单，打开详情抽屉
          const existingRecord = response.rows[0];
          this.handleView(existingRecord);
          // this.$message.info(`已找到质检单号 ${iqcNo} 的异常反馈单，正在查看详情`);
        } else {
          // 不存在异常反馈单，打开新建抽屉并预填充数据
          await this.handleAddWithPreFill(iqcNo);
        }
      } catch (error) {
        console.error("检查异常反馈单时出错:", error);
        this.$message.error("检查异常反馈单失败，请重试");
      }
    },

    // 新建异常反馈单并预填充数据
    async handleAddWithPreFill(iqcNo) {
      try {
        this.reset();

        // 根据质检单号获取质检单据信息
        const billResponse = await this.getBillByIqcNo(iqcNo);

        if (billResponse) {
          // 获取不良数量总和
          const poorCount = await this.calculatePoorCount(billResponse.id);

          // 计算不良率 (不良数量/总数量 * 100)
          const totalQty = parseInt(billResponse.qty) || 0;
          let poorRate = 0;
          if (totalQty > 0) {
            poorRate = ((poorCount / totalQty) * 100).toFixed(2);
          }

          // 预填充表单数据
          this.form.inspBillNo = iqcNo;
          this.form.materialName = billResponse.materialName;
          this.form.materialCode = billResponse.materialCode;
          this.form.materialId = billResponse.materialId;
          this.form.qty = billResponse.qty;
          this.form.poorCount = poorCount; // 设置不良数量
          this.form.poorRate = poorRate; // 设置不良率
          this.form.abnormalDate = new Date().toISOString().split("T")[0]; // 当前日期
          // 设置异常来源默认为来料
          this.setDefaultAbnormalSource();

          this.open = true;
          this.title = "新增单据异常反馈单";
        } else {
          this.handleAdd();
        }
      } catch (error) {
        console.error("预填充数据时出错:", error);
        this.handleAdd();
      }
    },

    // 根据质检单号获取质检单据信息
    async getBillByIqcNo(iqcNo) {
      try {
        // 调用质检单据的列表查询接口
        const queryParams = {
          pageNum: 1,
          pageSize: 1,
          iqcNo: iqcNo,
        };

        const response = await listBill(queryParams);

        if (response && response.rows && response.rows.length > 0) {
          return response.rows[0];
        }
        return null;
      } catch (error) {
        console.error("获取质检单据信息失败:", error);
        return null;
      }
    },

    // 计算不良数量总和
    async calculatePoorCount(billId) {
      try {
        // 根据质检单ID查询检验结果
        const queryParams = {
          pageNum: 1,
          pageSize: 1000, // 获取所有检验项目
          billId: billId,
        };

        const response = await listResult(queryParams);

        if (response && response.rows && response.rows.length > 0) {
          // 计算所有检验项目的不良数量总和
          const totalPoorCount = response.rows.reduce((total, item) => {
            const qtyDefect = parseInt(item.qtyDefect) || 0;
            return total + qtyDefect;
          }, 0);

          return totalPoorCount;
        }

        return 0; // 如果没有检验结果，返回0
      } catch (error) {
        console.error("计算不良数量失败:", error);
        return 0;
      }
    },

    // 更新不良数量和不良率
    async updatePoorCountAndRate() {
      try {
        // 根据质检单号获取质检单据信息
        const billResponse = await this.getBillByIqcNo(this.form.inspBillNo);

        if (billResponse) {
          // 计算不良数量
          const poorCount = await this.calculatePoorCount(billResponse.id);
          this.form.poorCount = poorCount;

          // 计算不良率 (不良数量/总数量 * 100)
          const totalQty =
            parseInt(this.form.qty) || parseInt(billResponse.qty) || 0;
          if (totalQty > 0) {
            const poorRate = ((poorCount / totalQty) * 100).toFixed(2);
            this.form.poorRate = poorRate;
          } else {
            this.form.poorRate = 0;
          }

          console.log(
            `更新不良数量: ${poorCount}, 不良率: ${this.form.poorRate}%`
          );
        }
      } catch (error) {
        console.error("更新不良数量和不良率失败:", error);
      }
    },

    // 设置异常来源默认为来料
    setDefaultAbnormalSource() {
      // 查找异常来源字典中"来料"对应的值
      const abnormalSourceOptions = this.dict.type.abnormal_source || [];
      const incomingOption = abnormalSourceOptions.find(
        (option) => option.value === "0" // 来料在字典中的值是0
      );

      if (incomingOption) {
        this.form.abnormalSource = incomingOption.value;
        console.log(
          "设置异常来源默认值为:",
          incomingOption.label,
          incomingOption.value
        );
      } else {
        // 如果找不到"来料"选项，使用第一个选项作为默认值
        if (abnormalSourceOptions.length > 0) {
          this.form.abnormalSource = abnormalSourceOptions[0].value;
          console.log(
            "未找到来料选项，使用第一个选项作为默认值:",
            abnormalSourceOptions[0].label
          );
        }
      }
    },

    // 获取完整的图片URL
    getImageUrl(path) {
      if (!path) return "";

      // 处理特殊字符路径 - 核心修复
      let encodedPath = path;

      // 处理双重编码问题（如果存在）
      if (path.includes("%25")) {
        encodedPath = decodeURIComponent(path);
      }

      // 编码特殊字符（保留斜杠）
      encodedPath = encodedPath
        .split("/")
        .map((segment) => encodeURIComponent(segment))
        .join("/");

      // 处理空格问题（确保空格被编码为%20）
      encodedPath = encodedPath.replace(/%20/g, " ");
      encodedPath = encodeURI(encodedPath);

      const baseUrl = process.env.VUE_APP_BASE_API || "";
      return `${baseUrl}${encodedPath}`;
    },

    // 从路径中提取文件名
    getFileName(path) {
      if (!path) return "";
      // 从路径中提取文件名，并替换空格
      const filename = path.split("/").pop();
      // 将文件名格式化为前端期望的格式
      return filename
        .replace("OIP-C ", "") // 移除 OIP-C 前缀
        .replace(/ /g, "") // 移除所有空格
        .replace("_2025", "_") // 简化日期部分
        .replace(/\.jpg$/, ""); // 移除扩展名（可选）
    } /** 查询单据异常反馈单列表 */,
    getList() {
      this.loading = true;
      listAbnormal(this.queryParams).then((response) => {
        this.abnormalList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        abnormalNo: null,
        inspBillId: null,
        inspBillNo: null,
        abnormalDate: null,
        qty: null,
        poorCount: null,
        abnormalSource: null,
        occursFrequency: null,
        poorRate: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        abnormalDesc: null,
        interimMeasures: null,
        reason: null,
        preventiveMeasure: null,
        results: null,
        picture: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        comId: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      // 设置异常来源默认为来料
      this.setDefaultAbnormalSource();
      this.open = true;
      this.title = "添加单据异常反馈单";
    },
    /** 查看按钮操作 */
    async handleView(row) {
      this.reset();
      const id = row.id || this.ids;
      try {
        const response = await getAbnormal(id);
        this.form = response.data;

        // 如果有质检单号，重新计算不良数量和不良率
        if (this.form.inspBillNo) {
          await this.updatePoorCountAndRate();
        }

        this.open = true;
        this.title = "查看单据异常反馈单";
        this.isView = true; // 添加标记，表示是查看模式
      } catch (error) {
        console.error("获取异常反馈单详情失败:", error);
        this.$message.error("获取详情失败，请重试");
      }
    },
    /** 修改按钮操作 */
    async handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      try {
        const response = await getAbnormal(id);
        this.form = response.data;

        // 如果有质检单号，重新计算不良数量和不良率
        if (this.form.inspBillNo) {
          await this.updatePoorCountAndRate();
        }

        this.open = true;
        this.title = "修改单据异常反馈单";
        this.isView = false; // 添加标记，表示是编辑模式
      } catch (error) {
        console.error("获取异常反馈单详情失败:", error);
        this.$message.error("获取详情失败，请重试");
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateAbnormal(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAbnormal(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除单据异常反馈单编号为"' + ids + '"的数据项？')
        .then(function () {
          return delAbnormal(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "quality/abnormal/export",
        {
          ...this.queryParams,
        },
        `单据异常反馈单_${new Date().toLocaleDateString()}.xlsx`
      );
    },

    // 判断是否为挑选处理结果
    isPickingResult(results) {
      // 字典值是 "2"，
      return results === "2";
    },

    // 处理挑选按钮点击
    handlePicking(row) {
      this.resetPickingForm();

      // 填充基本信息
      this.pickingForm.abnormalNo = row.abnormalNo;
      this.pickingForm.inspBillNo = row.inspBillNo;
      this.pickingForm.materialName = row.materialName;
      this.pickingForm.materialCode = row.materialCode;
      this.pickingForm.totalQty = parseInt(row.qty) || 0;

      // 初始化挑选明细数据
      this.initPickingDetailList(row);

      this.pickingDialogVisible = true;
    },

    // 初始化挑选明细列表
    async initPickingDetailList(row) {
      try {
        // 构建查询参数，直接使用质检单号
        const queryParams = {};

        // 物料编码 - 从异常反馈单中获取
        if (row.materialCode) {
          queryParams.materialCode = row.materialCode;
        }

        // 质检单号 - 直接传递质检单号
        if (row.inspBillNo) {
          queryParams.inspBillNo = row.inspBillNo;
        } else {
          this.$message.error('质检单号不能为空');
          return;
        }

        console.log('查询收货单箱信息参数:', queryParams);

        // 调用后端API查询收货单箱信息
        const response = await queryReceiveBox(queryParams);
        console.log(response.rows,"dhajkuw")
        // 处理后端返回的TableDataInfo格式数据
        let dataList = [];
        if (response && response.rows && Array.isArray(response.rows)) {
          dataList = response.rows;
        } else if (response && Array.isArray(response)) {
          dataList = response;
        }

        if (dataList.length > 0) {
          // 将后端返回的数据转换为前端表格需要的格式
          this.pickingDetailList = dataList.map((item, index) => ({
            id: item.id || index + 1,
            labelNo: item.boxNo || item.boxCode || `BX${String(index + 1).padStart(3, '0')}`, // 标签号与箱号保持一致
            boxNo: item.boxNo || item.boxCode || `BX${String(index + 1).padStart(3, '0')}`, // 箱号
            materialCode: item.materialCode || row.materialCode || '',
            materialName: item.materialName || row.materialName || '',
            quantity: parseInt(item.quantity) || parseInt(item.qty) || 0,
            qualifiedQty: 0 ,// 初始化为0，用户手动输入，不合格数会自动计算
            iqcNo:item.iqcNo
          }));

          console.log('获取到的挑选明细数据:', this.pickingDetailList);
        } else {
          // 如果没有数据，显示空列表
          this.pickingDetailList = [];
          this.$message.info('未找到相关的收货单箱信息');
        }
      } catch (error) {
        console.error('获取收货单箱信息失败:', error);
        this.$message.error('获取收货单箱信息失败，请重试');
        // 出错时使用空列表
        this.pickingDetailList = [];
      }
    },

    // 重置挑选表单
    resetPickingForm() {
      this.pickingForm = {
        abnormalNo: '',
        inspBillNo: '',
        materialName: '',
        materialCode: '',
        totalQty: 0,
        labelNo: ''
      };
      this.pickingDetailList = [];
    },

    // 合格数量变化处理
    handleQualifiedQtyChange(row) {
      // 确保合格数不超过总数量
      if (row.qualifiedQty > row.quantity) {
        row.qualifiedQty = row.quantity;
        this.$message.warning('合格数不能超过总数量');
      }
      if (row.qualifiedQty < 0) {
        row.qualifiedQty = 0;
      }
      // 不合格数会自动计算，无需手动处理
    },

    // 删除挑选明细
    handleDeletePickingDetail(index) {
      this.$confirm('确认删除该条挑选明细吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.pickingDetailList.splice(index, 1);
        this.$message.success('删除成功');
      }).catch(() => {
        // 用户取消删除
      });
    },

    // 取消挑选
    cancelPicking() {
      this.pickingDialogVisible = false;
      this.resetPickingForm();
    },

    // 提交挑选
    async submitPicking() {
      // 验证挑选明细数据
      if (this.pickingDetailList.length === 0) {
        this.$message.error("请添加挑选明细数据");
        return;
      }

      // 验证每行数据的合格数
      for (let i = 0; i < this.pickingDetailList.length; i++) {
        const item = this.pickingDetailList[i];
        if (item.qualifiedQty > item.quantity) {
          this.$message.error(`第${i + 1}行：合格数不能超过总数量`);
          return;
        }
        if (item.qualifiedQty < 0) {
          this.$message.error(`第${i + 1}行：合格数不能为负数`);
          return;
        }
      }

      // 显示加载状态
      const loading = this.$loading({
        lock: true,
        text: '正在提交挑选信息...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      try {
        // 需要发送QcInspAbnormalOpt对象数组
        console.log('原始挑选明细数据:', this.pickingDetailList);
        console.log('挑选表单数据:', this.pickingForm);

        // 构建List<QcInspAbnormalOpt>数组
        const submitData = this.pickingDetailList.map((item, index) => {
          const qcInspAbnormalOpt = {
            // 根据后端QcInspAbnormalOpt对象的字段结构构建数据
            id: item.id || null,
            qrCode: item.labelNo || item.boxNo || null, // 标签号
            boxNo: item.boxNo || item.labelNo || null, // 箱号字段
            materialCode: item.materialCode || this.pickingForm.materialCode || null,
            materialName: item.materialName || this.pickingForm.materialName || null,
            qty: item.quantity || null, // 总数量
            qualifiedQty: item.qualifiedQty || null, // 合格数量
            unqualifiedQty: (item.quantity || 0) - (item.qualifiedQty || 0) || null, // 不合格数量
            partId: null, // 部件ID
            abnormalId:  item.iqcNo , // 异常ID
          };

          console.log(`构建第${index + 1}条QcInspAbnormalOpt数据:`, {
            原始数据: item,
            构建后: qcInspAbnormalOpt
          });

          return qcInspAbnormalOpt;
        });

        console.log('提交挑选信息（List<QcInspAbnormalOpt>格式）:', submitData);

        // 调用后端API确认挑选
        const response = await confirmPicking(submitData);

        if (response && response.code === 200) {
          this.$modal.msgSuccess("挑选信息提交成功");
          this.pickingDialogVisible = false;
          this.resetPickingForm();

          // 刷新列表
          this.getList();
        } else {
          this.$message.error(response.msg || "提交失败，请重试");
        }
      } catch (error) {
        console.error('提交挑选信息失败:', error);
        this.$message.error("提交失败，请重试");
      } finally {
        // 关闭加载状态
        loading.close();
      }
    },
  },
};
</script>
